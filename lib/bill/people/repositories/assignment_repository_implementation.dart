import '../../../local_storage/models/local_storage_key.dart';
import '../../../local_storage/services/local_storage_service.dart';
import '../models/assignment.dart';
import 'assignment_repository.dart';

class AssignmentRepositoryImplementation implements AssignmentRepository {
  AssignmentRepositoryImplementation({
    required LocalStorageService localStorageService,
  }) : _localStorageService = localStorageService;

  final LocalStorageService _localStorageService;

  @override
  Future<List<Assignment>> getAssignmentList() async {
    final assignmentListJson = await _localStorageService.getValue<List<dynamic>>(
      LocalStorageKey.assignments,
    );
    if (assignmentListJson == null) return [];
    return assignmentListJson.map((assignmentJson) => Assignment.fromJson(assignmentJson)).toList();
  }

  Future<void> _saveAssignmentList(List<Assignment> assignments) {
    return _localStorageService.setValue(
      LocalStorageKey.assignments,
      assignments.map((assignment) => assignment.toJson()).toList(),
    );
  }

  @override
  Future<List<Assignment>> createAssignment(Assignment assignment) async {
    final assignmentList = await getAssignmentList();
    
    // Check if assignment with same itemId and personId already exists
    final existingIndex = assignmentList.indexWhere(
      (existing) => existing.itemId == assignment.itemId && existing.personId == assignment.personId,
    );
    
    if (existingIndex != -1) {
      // Replace existing assignment
      assignmentList[existingIndex] = assignment;
    } else {
      // Add new assignment
      assignmentList.add(assignment);
    }
    
    await _saveAssignmentList(assignmentList);
    return assignmentList;
  }

  @override
  Future<List<Assignment>> updateAssignment(Assignment assignment) async {
    final assignmentList = await getAssignmentList();
    final assignmentIndex = assignmentList.indexWhere(
      (existing) => existing.itemId == assignment.itemId && existing.personId == assignment.personId,
    );
    
    if (assignmentIndex == -1) {
      throw ArgumentError('Assignment with itemId "${assignment.itemId}" and personId "${assignment.personId}" not found');
    }
    
    assignmentList[assignmentIndex] = assignment;
    await _saveAssignmentList(assignmentList);
    return assignmentList;
  }

  @override
  Future<List<Assignment>> deleteAssignment(String itemId, String personId) async {
    final assignmentList = await getAssignmentList();

    // Check if assignment exists
    final assignmentIndex = assignmentList.indexWhere(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
    );
    
    if (assignmentIndex == -1) {
      throw ArgumentError('Assignment with itemId "$itemId" and personId "$personId" not found');
    }

    assignmentList.removeAt(assignmentIndex);
    await _saveAssignmentList(assignmentList);
    return assignmentList;
  }
}
