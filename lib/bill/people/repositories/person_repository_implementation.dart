import '../../../local_storage/models/local_storage_key.dart';
import '../../../local_storage/services/local_storage_service.dart';
import '../../../unique_identifier/services/unique_identifier_service.dart';
import '../models/person.dart';
import 'person_repository.dart';

class PersonRepositoryImplementation implements PersonRepository {
  PersonRepositoryImplementation({
    required LocalStorageService localStorageService,
    required UniqueIdentifierService uniqueIdentifierService,
  }) : _localStorageService = localStorageService,
       _uniqueIdentifierService = uniqueIdentifierService;

  final LocalStorageService _localStorageService;
  final UniqueIdentifierService _uniqueIdentifierService;

  @override
  Future<List<Person>> getPeopleList() async {
    final peopleListJson = await _localStorageService.getValue<List<dynamic>>(
      LocalStorageKey.people,
    );
    if (peopleListJson == null) return [];
    return peopleListJson.map((personJson) => Person.fromJson(personJson)).toList();
  }

  Future<void> _savePeopleList(List<Person> people) {
    return _localStorageService.setValue(
      LocalStorageKey.people,
      people.map((person) => person.toJson()).toList(),
    );
  }

  @override
  Person generatePerson() {
    final newPersonId = _uniqueIdentifierService.generate();
    return Person(id: newPersonId);
  }

  @override
  Future<List<Person>> createPerson([Person? person]) async {
    final peopleList = await getPeopleList();
    final newPerson = person ?? generatePerson();
    peopleList.add(newPerson);
    await _savePeopleList(peopleList);
    return peopleList;
  }

  @override
  Future<List<Person>> updatePerson(Person person) async {
    final peopleList = await getPeopleList();
    final personIndex = peopleList.indexWhere((index) => index.id == person.id);
    peopleList[personIndex] = person;
    await _savePeopleList(peopleList);
    return peopleList;
  }

  @override
  Future<List<Person>> deletePerson(String personId) async {
    final peopleList = await getPeopleList();

    // Check if person exists
    final personIndex = peopleList.indexWhere((index) => index.id == personId);
    if (personIndex == -1) {
      throw ArgumentError('Person with id "$personId" not found');
    }

    // Prevent deletion if this is the only person in the list
    if (peopleList.length == 1) {
      throw StateError('Cannot delete the only person in the list');
    }

    peopleList.removeAt(personIndex);
    await _savePeopleList(peopleList);
    return peopleList;
  }
}