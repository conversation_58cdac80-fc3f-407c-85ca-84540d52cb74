import 'package:flutter/material.dart';

import '../../items/models/item.dart';
import '../models/assignment.dart';
import '../models/person.dart';

abstract class PeopleAssignmentState extends ChangeNotifier {
  List<Item> get itemList;
  void toggleItemExpansion(String itemId);
  bool isItemExpanded(String itemId);

  List<Person> get peopleList;
  void addPerson();
  void removePerson(String personId);
  void onPersonNameChanged(String personId, String name);

  List<Assignment> get assignmentList;
  String getAssignmentSummaryForItem(String itemId);
  bool isPersonAssignedToItem(String itemId, String personId);
  void assignPersonToItem(String itemId, String personId);
  void unassignPersonFromItem(String itemId, String personId);
  void increaseQuantity(String itemId, String personId);
  void decreaseQuantity(String itemId, String personId);

  void returnToItemDefinition();
  void continueToPayment();
}
