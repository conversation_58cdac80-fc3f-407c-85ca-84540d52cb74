import 'package:flutter/material.dart';

import '../../items/models/item.dart';
import '../../items/models/tip.dart';
import '../../items/repositories/item_repository.dart';
import '../../items/repositories/tip_repository.dart';
import '../models/assignment.dart';
import '../models/person.dart';
import '../repositories/assignment_repository.dart';
import '../repositories/person_repository.dart';
import 'people_assignment_state.dart';

class PeopleAssignmentStateImplementation extends ChangeNotifier
    implements PeopleAssignmentState {
  PeopleAssignmentStateImplementation({
    required ItemRepository itemRepository,
    required TipRepository tipRepository,
    required PersonRepository personRepository,
    required AssignmentRepository assignmentRepository,
  }) : _itemRepository = itemRepository,
       _tipRepository = tipRepository,
       _personRepository = personRepository,
       _assignmentRepository = assignmentRepository {
    _loadData();
  }

  final ItemRepository _itemRepository;
  final TipRepository _tipRepository;
  final PersonRepository _personRepository;
  final AssignmentRepository _assignmentRepository;

  List<Item> _itemList = [];
  @override
  List<Item> get itemList => _itemList;

  List<Person> _peopleList = [];
  @override
  List<Person> get peopleList => _peopleList;

  List<Assignment> _assignmentList = [];
  @override
  List<Assignment> get assignmentList => _assignmentList;

  Tip? _tip;

  final Set<String> _expandedItemIds = {};

  Future<void> _loadData() async {
    await Future.wait([
      _loadItemList(),
      _loadPeopleList(),
      _loadAssignmentList(),
      _loadTip(),
    ]);
    
    // UI-003: If no people exist, one person is created automatically when first item is shown
    if (_peopleList.isEmpty) {
      await _createFirstPerson();
    }
    
    // UI-002: First item starts expanded showing people list
    if (_itemList.isNotEmpty) {
      _expandedItemIds.add(_itemList.first.id);
    }
    
    notifyListeners();
  }

  Future<void> _loadItemList() async {
    _itemList = await _itemRepository.getItemList();
  }

  Future<void> _loadPeopleList() async {
    _peopleList = await _personRepository.getPeopleList();
  }

  Future<void> _loadAssignmentList() async {
    _assignmentList = await _assignmentRepository.getAssignmentList();
  }

  Future<void> _loadTip() async {
    final didInitializeTip = await _tipRepository.checkIfTipHasBeenInitialized();
    if (didInitializeTip) {
      _tip = await _tipRepository.getTip();
    }
  }

  Future<void> _createFirstPerson() async {
    final newPerson = _personRepository.generatePerson();
    _peopleList.add(newPerson);
    _personRepository.createPerson(newPerson).ignore();
  }

  @override
  void toggleItemExpansion(String itemId) {
    if (_expandedItemIds.contains(itemId)) {
      // UI-008: When assign people button is pressed on expanded item, item collapses
      _expandedItemIds.remove(itemId);
    } else {
      // UI-007: When assign people button is pressed, people list expands and other items collapse
      // TR-010: Only one item can be expanded at a time
      _expandedItemIds.clear();
      _expandedItemIds.add(itemId);
    }
    notifyListeners();
  }

  @override
  bool isItemExpanded(String itemId) {
    return _expandedItemIds.contains(itemId);
  }

  @override
  void addPerson() {
    final newPerson = _personRepository.generatePerson();
    _peopleList.add(newPerson);
    notifyListeners();
    _personRepository.createPerson(newPerson).ignore();
  }

  @override
  void removePerson(String personId) {
    // BR-005: When a person is deleted, equally split amounts assigned to that person 
    // are redistributed equally among remaining assigned people
    // BR-006: When equal redistribution is not possible, remaining amount is distributed 
    // randomly among assigned people
    
    try {
      // Remove person from people list
      _peopleList.removeWhere((person) => person.id == personId);
      
      // Get assignments for this person
      final personAssignments = _assignmentList
          .where((assignment) => assignment.personId == personId)
          .toList();
      
      // Remove assignments for this person
      _assignmentList.removeWhere((assignment) => assignment.personId == personId);
      
      // Redistribute amounts for each item this person was assigned to
      for (final assignment in personAssignments) {
        _redistributeAssignmentAmount(assignment.itemId, assignment.amount);
      }
      
      notifyListeners();
      
      // Fire-and-forget storage operations
      _personRepository.deletePerson(personId).ignore();
      for (final assignment in personAssignments) {
        _assignmentRepository.deleteAssignment(assignment.itemId, personId).ignore();
      }
      _saveUpdatedAssignments();
      
    } catch (e) {
      // TR-015: Storage failures do not interrupt user workflow or display error messages
      // Silently handle errors
    }
  }

  void _redistributeAssignmentAmount(String itemId, double amountToRedistribute) {
    final itemAssignments = _assignmentList
        .where((assignment) => assignment.itemId == itemId)
        .toList();
    
    if (itemAssignments.isEmpty) return;
    
    // BR-005: Redistribute equally among remaining assigned people
    final amountPerPerson = amountToRedistribute / itemAssignments.length;
    
    for (int i = 0; i < itemAssignments.length; i++) {
      final assignment = itemAssignments[i];
      final newAmount = assignment.amount + amountPerPerson;
      
      final assignmentIndex = _assignmentList.indexWhere(
        (a) => a.itemId == assignment.itemId && a.personId == assignment.personId,
      );
      
      if (assignmentIndex != -1) {
        _assignmentList[assignmentIndex] = assignment.copyWith(amount: newAmount);
      }
    }
  }

  @override
  void onPersonNameChanged(String personId, String name) {
    final personIndex = _peopleList.indexWhere((person) => person.id == personId);
    if (personIndex != -1) {
      final updatedPerson = _peopleList[personIndex].copyWith(name: name);
      _peopleList[personIndex] = updatedPerson;
      notifyListeners();
      _personRepository.updatePerson(updatedPerson).ignore();
    }
  }

  @override
  String getAssignmentSummaryForItem(String itemId) {
    final itemAssignments = _assignmentList
        .where((assignment) => assignment.itemId == itemId)
        .toList();
    
    if (itemAssignments.isEmpty) return '';
    
    // UI-010: Assignment summary examples
    if (itemAssignments.length == 1) {
      // Single person: "Peter: $17.00"
      final assignment = itemAssignments.first;
      final person = _peopleList.firstWhere((p) => p.id == assignment.personId);
      final personName = person.name.isEmpty ? _getPersonNumber(person.id) : person.name;
      return '$personName: \$${assignment.amount.toStringAsFixed(2)}';
    }
    
    // Check if all assignments have equal amounts (equal split)
    final firstAmount = itemAssignments.first.amount;
    final isEqualSplit = itemAssignments.every((a) => a.amount == firstAmount);
    
    if (isEqualSplit) {
      // Equal split: "4 and Bob: $8.50 each"
      final names = itemAssignments.map((assignment) {
        final person = _peopleList.firstWhere((p) => p.id == assignment.personId);
        return person.name.isEmpty ? _getPersonNumber(person.id) : person.name;
      }).join(' and ');
      return '$names: \$${firstAmount.toStringAsFixed(2)} each';
    } else {
      // Unequal split: "Charlie: $4.00 (1), 2: $12.00 (3)"
      final summaries = itemAssignments.map((assignment) {
        final person = _peopleList.firstWhere((p) => p.id == assignment.personId);
        final personName = person.name.isEmpty ? _getPersonNumber(person.id) : person.name;
        final quantity = assignment.quantity > 0 ? ' (${assignment.quantity})' : '';
        return '$personName: \$${assignment.amount.toStringAsFixed(2)}$quantity';
      }).join(', ');
      return summaries;
    }
  }

  String _getPersonNumber(String personId) {
    final personIndex = _peopleList.indexWhere((person) => person.id == personId);
    return (personIndex + 1).toString();
  }

  @override
  bool isPersonAssignedToItem(String itemId, String personId) {
    return _assignmentList.any(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
    );
  }

  @override
  void assignPersonToItem(String itemId, String personId) {
    // BR-017: When person is checked and quantity is 0, quantity is updated to 1
    final existingAssignment = _assignmentList.firstWhere(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
      orElse: () => Assignment(itemId: itemId, personId: personId),
    );
    
    final item = _itemList.firstWhere((item) => item.id == itemId);
    final quantity = existingAssignment.quantity == 0 ? 1 : existingAssignment.quantity;
    final amount = _calculateAssignmentAmount(item, quantity);
    
    final newAssignment = Assignment(
      itemId: itemId,
      personId: personId,
      quantity: quantity,
      amount: amount,
    );
    
    final existingIndex = _assignmentList.indexWhere(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
    );
    
    if (existingIndex != -1) {
      _assignmentList[existingIndex] = newAssignment;
    } else {
      _assignmentList.add(newAssignment);
    }
    
    notifyListeners();
    _assignmentRepository.createAssignment(newAssignment).ignore();
  }

  @override
  void unassignPersonFromItem(String itemId, String personId) {
    // BR-016: When person is unchecked and quantity is integer, quantity is updated to 0
    _assignmentList.removeWhere(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
    );
    
    notifyListeners();
    _assignmentRepository.deleteAssignment(itemId, personId).ignore();
  }

  @override
  void increaseQuantity(String itemId, String personId) {
    final assignmentIndex = _assignmentList.indexWhere(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
    );
    
    if (assignmentIndex != -1) {
      final assignment = _assignmentList[assignmentIndex];
      final item = _itemList.firstWhere((item) => item.id == itemId);
      final newQuantity = assignment.quantity + 1;
      final newAmount = _calculateAssignmentAmount(item, newQuantity);
      
      final updatedAssignment = assignment.copyWith(
        quantity: newQuantity,
        amount: newAmount,
      );
      
      _assignmentList[assignmentIndex] = updatedAssignment;
      notifyListeners();
      _assignmentRepository.updateAssignment(updatedAssignment).ignore();
    }
  }

  @override
  void decreaseQuantity(String itemId, String personId) {
    final assignmentIndex = _assignmentList.indexWhere(
      (assignment) => assignment.itemId == itemId && assignment.personId == personId,
    );
    
    if (assignmentIndex != -1) {
      final assignment = _assignmentList[assignmentIndex];
      
      if (assignment.quantity > 1) {
        final item = _itemList.firstWhere((item) => item.id == itemId);
        final newQuantity = assignment.quantity - 1;
        final newAmount = _calculateAssignmentAmount(item, newQuantity);
        
        final updatedAssignment = assignment.copyWith(
          quantity: newQuantity,
          amount: newAmount,
        );
        
        _assignmentList[assignmentIndex] = updatedAssignment;
        notifyListeners();
        _assignmentRepository.updateAssignment(updatedAssignment).ignore();
      }
    }
  }

  double _calculateAssignmentAmount(Item item, int quantity) {
    // BR-021: Amount updates automatically when quantity or assignment status changes
    // BR-022: Total calculation includes tip when tip exists
    // BR-023: Tip calculation: tip amount = assigned amount × (tip percentage ÷ 100)
    
    final baseAmount = (item.price * quantity);
    if (_tip != null) {
      final tipAmount = baseAmount * (_tip!.percentage / 100);
      return baseAmount + tipAmount;
    }
    return baseAmount;
  }

  void _saveUpdatedAssignments() {
    for (final assignment in _assignmentList) {
      _assignmentRepository.updateAssignment(assignment).ignore();
    }
  }

  @override
  void returnToItemDefinition() {
    // UI-032: Return button collapses all items and returns to previous step
    _expandedItemIds.clear();
    notifyListeners();
    // TODO: Implement navigation to previous step
  }

  @override
  void continueToPayment() {
    // UI-033: Continue button proceeds to next step only when all items are fully assigned
    // TR-005: All items must be fully assigned before proceeding to next step
    // TR-016: Assignment validation occurs when continue button is pressed
    
    if (_areAllItemsFullyAssigned()) {
      // TODO: Implement navigation to next step
    }
    // If validation fails, user stays on current step
  }

  bool _areAllItemsFullyAssigned() {
    for (final item in _itemList) {
      final itemAssignments = _assignmentList
          .where((assignment) => assignment.itemId == item.id)
          .toList();
      
      final totalAssignedQuantity = itemAssignments
          .fold(0, (sum, assignment) => sum + assignment.quantity);
      
      if (totalAssignedQuantity < item.quantity) {
        return false;
      }
    }
    return true;
  }
}
