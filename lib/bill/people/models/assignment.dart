class Assignment {
  const Assignment({
    required this.itemId,
    required this.personId,
    this.quantity = 0,
    this.amount = 0,
  });

  final String itemId;
  final String personId;
  /// Quantity of the item quantity assigned to the person. 0 means equal split.
  final int quantity;
  /// Amount of the item total amount assigned to the person.
  final double amount;

  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'personId': personId,
      'quantity': quantity,
      'amount': amount,
    };
  }

  factory Assignment.fromJson(Map<String, dynamic> json) {
    return Assignment(
      itemId: json['itemId'],
      personId: json['personId'],
      quantity: json['quantity'],
      amount: json['amount'],
    );
  }

  Assignment copyWith({int? quantity, double? amount}) {
    return Assignment(
      itemId: itemId,
      personId: personId,
      quantity: quantity ?? this.quantity,
      amount: amount ?? this.amount,
    );
  }
}
