class Person {
  const Person({
    required this.id,
    this.name = '',
    this.owedAmount = 0,
    this.payedAmount = 0,
  });

  final String id;
  final String name;
  final double owedAmount;
  final double payedAmount;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'owedAmount': owedAmount,
      'payedAmount': payedAmount,
    };
  }

  factory Person.fromJson(Map<String, dynamic> json) {
    return Person(
      id: json['id'],
      name: json['name'],
      owedAmount: json['owedAmount'],
      payedAmount: json['payedAmount'],
    );
  }

  Person copyWith({String? name, double? owedAmount, double? payedAmount}) {
    return Person(
      id: id,
      name: name ?? this.name,
      owedAmount: owedAmount ?? this.owedAmount,
      payedAmount: payedAmount ?? this.payedAmount,
    );
  }
}
