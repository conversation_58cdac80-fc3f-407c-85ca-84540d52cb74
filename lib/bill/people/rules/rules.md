# People Assignment Rules

## Business Rules (Design-Independent)

### Person Management
- **BR-001**: Person number is assigned automatically based on order in list and cannot be edited
- **BR-002**: Person name can be edited as free text
- **BR-003**: The same people appear in all items for assignment, regardless of which item they were created in
- **BR-004**: When a person is added while in a particular item, that person becomes available (but not assigned) to all other items
- **BR-005**: When a person is deleted, equally split amounts assigned to that person are redistributed equally among remaining assigned people
- **BR-006**: When equal redistribution is not possible, remaining amount is distributed randomly among assigned people
- **BR-007**: Person quantity starts with equal split text when no people are assigned or all assigned people show equal split
- **BR-008**: Person quantity starts with 0 when assigned people show integer quantities

### Quantity Management
- **BR-009**: When quantity changes to equal split, amount is updated by equal split calculation for all assigned people
- **BR-010**: When quantity changes to integer, amount is updated by quantity calculation for all assigned people
- **BR-011**: Equal split calculation: amount = item total ÷ number of assigned people
- **BR-012**: Quantity calculation: amount = item price × quantity
- **BR-013**: When person is assigned with equal split, checkbox starts checked
- **BR-014**: When person is assigned with integer quantity and unassigned quantity exists, checkbox starts checked
- **BR-015**: When person is assigned with integer quantity and no unassigned quantity exists, checkbox starts disabled

### Assignment Management
- **BR-016**: When person is unchecked and quantity is integer, quantity is updated to 0
- **BR-017**: When person is checked and quantity is 0, quantity is updated to 1
- **BR-018**: When all quantity is assigned, checkboxes for unassigned people are disabled
- **BR-019**: Unassigned quantity calculation: item quantity - sum of assigned quantities

### Amount Calculations
- **BR-020**: Amount is currency format and cannot be directly edited
- **BR-021**: Amount updates automatically when quantity or assignment status changes
- **BR-022**: Total calculation includes tip when tip exists: assigned amount + corresponding tip
- **BR-023**: Tip calculation: tip amount = assigned amount × (tip percentage ÷ 100)

## User Interface Rules (Design-Focused)

### Initial State
- **UI-001**: Page shows list of items, total section, return button, and continue button
- **UI-002**: First item starts expanded showing people list
- **UI-003**: If no people exist, one person is created automatically when first item is shown
- **UI-004**: When second person is added, first person's checkbox is enabled

### Item Display
- **UI-005**: Item shows order number, editable label, values text, and assign people button
- **UI-006**: Values text format: "$[price] x [quantity] = $[total]" (e.g., "$8.50 x 2 = $17.00")
- **UI-007**: When assign people button is pressed, people list expands and other items collapse
- **UI-008**: When assign people button is pressed on expanded item, item collapses and people list hides
- **UI-009**: Collapsed item with assigned people shows assignment summary paragraph
- **UI-010**: Assignment summary examples:
  - Single person: "Peter: $17.00"
  - Equal split: "4 and Bob: $8.50 each"
  - Unequal split: "Charlie: $4.00 (1), 2: $12.00 (3)"
- **UI-011**: Collapsed item shows unassigned quantity when applicable: "Unassigned quantity: 1"

### People List Display
- **UI-012**: People list shows list of people, add person button, and hide list button
- **UI-013**: Hide button on non-last item automatically opens next item's people list
- **UI-014**: Person shows number, name field, quantity controls, amount display, and checkbox
- **UI-015**: Delete button shown only when more than one person exists

### Quantity Controls
- **UI-016**: Add button starts enabled when more than one person exists
- **UI-017**: Add button starts disabled when only one person exists
- **UI-018**: Subtract button starts disabled when quantity is equal split text
- **UI-019**: When add button pressed on equal split, quantity becomes 1 and subtract button enables
- **UI-020**: When add button pressed on integer, quantity increases by 1
- **UI-021**: When subtract button pressed on quantity 1, quantity becomes equal split and subtract button disables
- **UI-022**: When subtract button pressed on quantity > 1, quantity decreases by 1

### Checkbox Behavior
- **UI-023**: When unchecked, add and subtract buttons are disabled
- **UI-024**: When unchecked with integer quantity, quantity updates to 0
- **UI-025**: When checked, add button is enabled
- **UI-026**: When checked with quantity 0, quantity updates to 1
- **UI-027**: When checked with equal split, amount updates by equal split calculation

### Total Display
- **UI-028**: Total shows two lines
- **UI-029**: Without tip: first line shows assigned amount like "$17.00"
- **UI-030**: With tip: first line shows "assigned amount + tip" like "$17.00 + $1.70"
- **UI-031**: Second line always shows total amount including tip if any

### Navigation
- **UI-032**: Return button collapses all items and returns to previous step
- **UI-033**: Continue button proceeds to next step only when all items are fully assigned
- **UI-034**: Continue button is always visible

## Technical Rules (Implementation Constraints)

### Data Validation
- **TR-001**: Person names have no character limit restrictions
- **TR-002**: Quantity must be non-negative integer when not equal split
- **TR-003**: Amount values support exactly 2 decimal places
- **TR-004**: At least one person must exist when people list is shown
- **TR-005**: All items must be fully assigned before proceeding to next step

### System Behavior
- **TR-006**: All calculations are performed in real-time
- **TR-007**: People assignment state is automatically saved to local device storage asynchronously
- **TR-008**: Storage operations are fire-and-forget (do not block UI or throw errors if failed)
- **TR-009**: Local storage is used for session persistence across app restarts
- **TR-010**: Only one item can be expanded at a time
- **TR-011**: Item expansion state is not persisted across app restarts

### Error Handling
- **TR-012**: Invalid quantity input displays appropriate error message
- **TR-013**: Division by zero in calculations is handled gracefully
- **TR-014**: All error messages are user-friendly and actionable
- **TR-015**: Storage failures do not interrupt user workflow or display error messages
- **TR-016**: Assignment validation occurs when continue button is pressed

## Examples and Edge Cases

### Example 1: Equal Split Assignment
- Item: Pizza, Total: $20.00, People: Alice, Bob
- Both assigned with equal split
- Expected: Alice: $10.00, Bob: $10.00

### Example 2: Quantity-Based Assignment
- Item: Drinks, Price: $3.00, Quantity: 4, People: Charlie (2), Dana (1)
- Expected: Charlie: $6.00 (2), Dana: $3.00 (1), Unassigned: 1

### Example 3: Mixed Assignment Display
- Item assigned to: Person 1 (quantity 2), Alice (equal split with Person 1)
- Display: "1: $6.00 (2), Alice: $6.00 (2)"

### Example 4: With Tip Calculation
- Assigned amount: $15.00, Tip percentage: 20%
- Expected tip: $3.00, Total: $18.00
- Display: "$15.00 + $3.00" (line 1), "$18.00" (line 2)

### Edge Case 1: Single Person Deletion
- When only one person exists, delete button should not be available
- Attempting to delete the last person should be prevented

### Edge Case 2: Full Assignment
- When all item quantity is assigned, remaining people checkboxes become disabled
- New people added when fully assigned start with disabled checkbox

### Edge Case 3: Person Deletion with Redistribution
- Person with assigned amount is deleted
- Equal split amounts are redistributed equally among remaining assigned people
- Fractional remainders are distributed randomly to maintain total accuracy
