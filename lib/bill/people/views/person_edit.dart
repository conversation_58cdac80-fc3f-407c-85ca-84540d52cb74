import 'package:flutter/material.dart';

import '../../../components/button/delete_button.dart';
import '../../../components/card/list_item_card.dart';
import '../../../components/info/list_item_card_header.dart';
import '../../../components/input/form_text_field.dart';

class PersonEdit extends StatelessWidget {
  const PersonEdit({
    super.key,
    required this.orderNumber,
    required this.nameLabel,
    this.name = '',
    required this.onNameChanged,
    required this.onDeletePressed,
    this.showDeleteButton = true,
    required this.onSubtractPressed,
    this.isSubtractEnabled = false,
    required this.onAddPressed,
    this.isAddEnabled = false,
    required this.quantityText,
    required this.amountText,
    required this.isAssigned,
    required this.onAssignedChanged,
  });

  final int orderNumber;
  final String nameLabel;
  final String name;
  final void Function(String name) onNameChanged;
  final void Function() onDeletePressed;
  final bool showDeleteButton;

  final void Function() onSubtractPressed;
  final bool isSubtractEnabled;
  final void Function() onAddPressed;
  final bool isAddEnabled;
  final String quantityText;
  final String amountText;
  final bool isAssigned;
  final void Function(bool?) onAssignedChanged;

  @override
  Widget build(BuildContext context) {
    return ListItemCard(
      child: Column(
        children: [
          // Header
          ListItemCardHeader(
            orderNumber: orderNumber,
            nameField: FormTextField(
              label: nameLabel,
              initialValue: name,
              onChanged: onNameChanged,
            ),
            iconButton: showDeleteButton
                ? DeleteButton(onPressed: onDeletePressed)
                : null,
          ),

          // Body
          const SizedBox(height: 12),
          Row(
            children: [
              // Subtract button
              IconButton(
                icon: const Icon(Icons.remove_circle_outline),
                onPressed: isSubtractEnabled ? onSubtractPressed : () {},
              ),

              // Quantity
              Expanded(
                child: Text(quantityText),
              ),

              // Add button
              IconButton(
                icon: const Icon(Icons.add_circle_outline),
                onPressed: isAddEnabled ? onAddPressed : () {},
              ),

              // Amount
              Expanded(
                child: Text(amountText),
              ),

              // Checkbox
              Checkbox(
                value: isAssigned,
                onChanged: onAssignedChanged,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
