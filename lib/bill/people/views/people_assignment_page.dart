import 'package:flutter/material.dart';

import '../../items/models/item.dart';
import 'item_assign.dart';

class PeopleAssignmentPage extends StatelessWidget {
  const PeopleAssignmentPage({
    super.key,
    required this.itemList,
    required this.nameLabel,
    required this.onNameChanged,
    required this.isItemExpanded,
    required this.onPeopleListIconPressed,
    required this.getAssignmentSummaryForItem,
    required this.peopleList,
    required this.addPersonLabel,
    required this.addPerson,
    required this.closeListLabel,
    required this.onCloseListPressed,
    required this.returnButton,
    required this.total,
    required this.continueButton,
  });

  final List<Item> itemList;
  final String nameLabel;
  final void Function(String itemId, String name) onNameChanged;
  final bool Function(String itemId) isItemExpanded;
  final void Function(String itemId) onPeopleListIconPressed;
  final String Function(String itemId) getAssignmentSummaryForItem;
  final Widget peopleList;
  final String addPersonLabel;
  final void Function() addPerson;
  final String closeListLabel;
  final void Function(String itemId) onCloseListPressed;
  final Widget returnButton;
  final Widget total;
  final Widget continueButton;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [returnButton, total, continueButton],
        ),
      ),
      body: SafeArea(
        child: ListView.separated(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemCount: itemList.length,
          itemBuilder: (context, index) {
            final item = itemList[index];
            return ItemAssign(
              orderNumber: index + 1,
              nameLabel: nameLabel,
              name: item.name,
              onNameChanged: (value) => onNameChanged(item.id, value),
              isExpanded: isItemExpanded(item.id),
              onPeopleListIconPressed: () => onPeopleListIconPressed(item.id),
              itemSummary: item.summary,
              assignmentSummary: getAssignmentSummaryForItem(item.id),
              peopleList: peopleList,
              addPersonLabel: addPersonLabel,
              onPersonAddPressed: addPerson,
              closeListLabel: closeListLabel,
              onCloseListPressed: () => onCloseListPressed(item.id),
            );
          },
        ),
      ),
    );
  }
}
