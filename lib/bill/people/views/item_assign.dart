import 'package:flutter/material.dart';

import '../../../components/card/list_item_card.dart';
import '../../../components/info/list_item_card_header.dart';
import '../../../components/input/form_text_field.dart';

class ItemAssign extends StatelessWidget {
  const ItemAssign({
    super.key,
    required this.orderNumber,
    required this.nameLabel,
    this.name = '',
    required this.onNameChanged,
    required this.isExpanded,
    required this.onPeopleListIconPressed,
    required this.itemSummary,
    this.assignmentSummary,
    required this.peopleList,
    required this.addPersonLabel,
    required this.onPersonAddPressed,
    required this.closeListLabel,
    required this.onCloseListPressed,
  });

  final int orderNumber;
  final String nameLabel;
  final String name;
  final void Function(String name) onNameChanged;
  final bool isExpanded;
  final void Function() onPeopleListIconPressed;
  final String itemSummary;
  final String? assignmentSummary;
  final Widget peopleList;
  final String addPersonLabel;
  final void Function() onPersonAddPressed;
  final String closeListLabel;
  final void Function() onCloseListPressed;

  @override
  Widget build(BuildContext context) {
    return ListItemCard(
      child: Column(
        children: [
          // Header
          ListItemCardHeader(
            orderNumber: orderNumber,
            nameField: FormTextField(
              label: nameLabel,
              initialValue: name,
              onChanged: onNameChanged,
            ),
            iconButton: IconButton(
              icon: const Icon(Icons.person_outline),
              onPressed: onPeopleListIconPressed,
            ),
          ),

          // Expanded view
          if (isExpanded) ...[
            // People list
            peopleList,

            // Buttons
            Row(
              children: [
                // Add person button
                ElevatedButton(
                  onPressed: onPersonAddPressed,
                  child: Text(addPersonLabel),
                ),

                // Close list button
                ElevatedButton(
                  onPressed: onCloseListPressed,
                  child: Text(closeListLabel),
                ),
              ],
            ),

            // Collapsed view
          ] else ...[
            // Item summary
            Text(itemSummary),

            // Assignment summary
            if (assignmentSummary != null) Text(assignmentSummary!),
          ],
        ],
      ),
    );
  }
}
