import 'package:flutter/material.dart';

import '../models/person.dart';
import 'person_edit.dart';

class PeopleList extends StatelessWidget {
  const PeopleList({
    super.key,
    required this.peopleList,
    required this.nameLabel,
    required this.onNameChanged,
    required this.onDeletePressed,
    required this.onSubtractPressed,
    required this.onAddPressed,
    required this.isPersonAssigned,
    required this.onAssignedChanged,
  });

  final List<Person> peopleList;
  final String nameLabel;
  final void Function(String personId, String name) onNameChanged;
  final void Function(String personId) onDeletePressed;
  final void Function(String personId) onSubtractPressed;
  final void Function(String personId) onAddPressed;
  final bool Function(String personId) isPersonAssigned;
  final void Function(String personId, bool isAssigned) onAssignedChanged;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemCount: peopleList.length,
      itemBuilder: (context, index) {
        final person = peopleList[index];
        return PersonEdit(
          orderNumber: index + 1,
          nameLabel: nameLabel,
          name: person.name,
          onNameChanged: (value) => onNameChanged(person.id, value),
          onDeletePressed: () => onDeletePressed(person.id),
          showDeleteButton: peopleList.length > 1,
          onSubtractPressed: () => onSubtractPressed(person.id),
          isSubtractEnabled: person.owedAmount > 0,
          onAddPressed: () => onAddPressed(person.id),
          isAddEnabled: person.payedAmount > 0,
          quantityText: person.owedAmount.toStringAsFixed(0),
          amountText: person.payedAmount.toStringAsFixed(2),
          isAssigned: isPersonAssigned(person.id),
          onAssignedChanged: (value) =>
              onAssignedChanged(person.id, value ?? false),
        );
      },
    );
  }
}
