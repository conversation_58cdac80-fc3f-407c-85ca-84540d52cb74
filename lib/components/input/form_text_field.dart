import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../utils/formatters.dart';

class FormTextField extends StatefulWidget {
  const FormTextField({
    super.key,
    this.prefixIcon,
    required this.label,
    required this.initialValue,
    required this.onChanged,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.autovalidateMode = AutovalidateMode.disabled,
  });

  FormTextField.currency({
    super.key,
    this.prefixIcon = Icons.payments,
    required this.label,
    required this.initialValue,
    required this.onChanged,
    this.validator,
    this.autovalidateMode = AutovalidateMode.disabled,
  }) : keyboardType = TextInputType.number,
       inputFormatters = [currencyFormatter];

  FormTextField.integer({
    super.key,
    this.prefixIcon = Icons.layers_outlined,
    required this.label,
    required this.initialValue,
    required this.onChanged,
    this.validator,
    this.autovalidateMode = AutovalidateMode.disabled,
  }) : keyboardType = TextInputType.number,
       inputFormatters = [numberFormatter];

  final IconData? prefixIcon;
  final String label;
  final String initialValue;
  final void Function(String) onChanged;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final AutovalidateMode autovalidateMode;

  @override
  State<FormTextField> createState() => _TextInputState();
}

class _TextInputState extends State<FormTextField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _controller.text = widget.initialValue;
  }

  @override
  void didUpdateWidget(FormTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialValue != widget.initialValue) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _controller.text = widget.initialValue;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      decoration: InputDecoration(
        labelText: widget.label,
        prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
      ),
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      onChanged: widget.onChanged,
      validator: widget.validator,
      autovalidateMode: widget.autovalidateMode,
    );
  }
}
