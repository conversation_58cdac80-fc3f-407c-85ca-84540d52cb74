import 'package:bill_splitter_2/bill/people/models/person.dart';
import 'package:bill_splitter_2/bill/people/repositories/person_repository_implementation.dart';
import 'package:bill_splitter_2/local_storage/models/local_storage_key.dart';
import 'package:bill_splitter_2/local_storage/services/local_storage_service.dart';
import 'package:bill_splitter_2/unique_identifier/services/unique_identifier_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLocalStorageService extends Mock implements LocalStorageService {
  @override
  Future<void> setValue<T>(LocalStorageKey key, T value) {
    return super.noSuchMethod(
      Invocation.method(#setValue, [key, value]),
    ) ?? Future<void>.value();
  }
}

class MockUniqueIdentifierService extends Mock implements UniqueIdentifierService {}

void main() {
  late MockLocalStorageService mockLocalStorageService;
  late MockUniqueIdentifierService mockUniqueIdentifierService;
  late PersonRepositoryImplementation personRepository;

  setUpAll(() {
    registerFallbackValue(LocalStorageKey.people);
  });

  setUp(() {
    mockLocalStorageService = MockLocalStorageService();
    mockUniqueIdentifierService = MockUniqueIdentifierService();

    // Set up default mock behavior for setValue
    when(() => mockLocalStorageService.setValue(any(), any()))
        .thenAnswer((_) => Future<void>.value());

    personRepository = PersonRepositoryImplementation(
      localStorageService: mockLocalStorageService,
      uniqueIdentifierService: mockUniqueIdentifierService,
    );
  });

  group('PersonRepositoryImplementation', () {
    group('getPeopleList', () {
      test('returns empty list when no people are stored', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => null);

        // Act
        final result = await personRepository.getPeopleList();

        // Assert
        expect(result, isEmpty);
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .called(1);
      });

      test('returns list of people when people are stored', () async {
        // Arrange
        final peopleJson = [
          {
            'id': 'person1',
            'name': 'John Doe',
            'owedAmount': 25.50,
            'payedAmount': 30.00,
          },
          {
            'id': 'person2',
            'name': 'Jane Smith',
            'owedAmount': 15.75,
            'payedAmount': 20.00,
          },
        ];
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => peopleJson);

        // Act
        final result = await personRepository.getPeopleList();

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('person1'));
        expect(result[0].name, equals('John Doe'));
        expect(result[0].owedAmount, equals(25.50));
        expect(result[0].payedAmount, equals(30.00));
        expect(result[1].id, equals('person2'));
        expect(result[1].name, equals('Jane Smith'));
        expect(result[1].owedAmount, equals(15.75));
        expect(result[1].payedAmount, equals(20.00));
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .called(1);
      });
    });

    group('generatePerson', () {
      test('generates a new person with unique ID and default values', () {
        // Arrange
        const generatedId = 'generated-id-123';
        when(() => mockUniqueIdentifierService.generate())
            .thenReturn(generatedId);

        // Act
        final result = personRepository.generatePerson();

        // Assert
        expect(result.id, equals(generatedId));
        expect(result.name, equals(''));
        expect(result.owedAmount, equals(0));
        expect(result.payedAmount, equals(0));
        verify(() => mockUniqueIdentifierService.generate()).called(1);
      });
    });

    group('createPerson', () {
      test('creates a new person with generated values when no person provided', () async {
        // Arrange
        const generatedId = 'generated-id-123';
        when(() => mockUniqueIdentifierService.generate())
            .thenReturn(generatedId);
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => null);

        // Act
        final result = await personRepository.createPerson();

        // Assert
        expect(result, hasLength(1));
        expect(result[0].id, equals(generatedId));
        expect(result[0].name, equals(''));
        expect(result[0].owedAmount, equals(0));
        expect(result[0].payedAmount, equals(0));
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .called(1);
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.people,
          any(),
        )).called(1);
      });

      test('creates a new person with provided person data', () async {
        // Arrange
        const existingPerson = Person(
          id: 'existing-id',
          name: 'Existing Person',
          owedAmount: 10.0,
          payedAmount: 15.0,
        );
        const newPerson = Person(
          id: 'new-id',
          name: 'New Person',
          owedAmount: 20.0,
          payedAmount: 25.0,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => [existingPerson.toJson()]);

        // Act
        final result = await personRepository.createPerson(newPerson);

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('existing-id'));
        expect(result[1].id, equals('new-id'));
        expect(result[1].name, equals('New Person'));
        expect(result[1].owedAmount, equals(20.0));
        expect(result[1].payedAmount, equals(25.0));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.people,
          any(),
        )).called(1);
      });
    });

    group('updatePerson', () {
      test('updates existing person in the list', () async {
        // Arrange
        const originalPerson = Person(
          id: 'person1',
          name: 'Original Name',
          owedAmount: 10.0,
          payedAmount: 15.0,
        );
        const updatedPerson = Person(
          id: 'person1',
          name: 'Updated Name',
          owedAmount: 20.0,
          payedAmount: 25.0,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => [originalPerson.toJson()]);

        // Act
        final result = await personRepository.updatePerson(updatedPerson);

        // Assert
        expect(result, hasLength(1));
        expect(result[0].id, equals('person1'));
        expect(result[0].name, equals('Updated Name'));
        expect(result[0].owedAmount, equals(20.0));
        expect(result[0].payedAmount, equals(25.0));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.people,
          any(),
        )).called(1);
      });

      test('updates correct person when multiple people exist', () async {
        // Arrange
        const person1 = Person(id: 'person1', name: 'Person 1');
        const person2 = Person(id: 'person2', name: 'Person 2');
        const person3 = Person(id: 'person3', name: 'Person 3');
        const updatedPerson2 = Person(
          id: 'person2',
          name: 'Updated Person 2',
          owedAmount: 30.0,
          payedAmount: 35.0,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => [
              person1.toJson(),
              person2.toJson(),
              person3.toJson(),
            ]);

        // Act
        final result = await personRepository.updatePerson(updatedPerson2);

        // Assert
        expect(result, hasLength(3));
        expect(result[0].name, equals('Person 1'));
        expect(result[1].name, equals('Updated Person 2'));
        expect(result[1].owedAmount, equals(30.0));
        expect(result[1].payedAmount, equals(35.0));
        expect(result[2].name, equals('Person 3'));
      });
    });

    group('deletePerson', () {
      test('deletes person from the list', () async {
        // Arrange
        const person1 = Person(id: 'person1', name: 'Person 1');
        const person2 = Person(id: 'person2', name: 'Person 2');
        const person3 = Person(id: 'person3', name: 'Person 3');
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => [
              person1.toJson(),
              person2.toJson(),
              person3.toJson(),
            ]);

        // Act
        final result = await personRepository.deletePerson('person2');

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('person1'));
        expect(result[1].id, equals('person3'));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.people,
          any(),
        )).called(1);
      });

      test('throws ArgumentError when person with given ID does not exist', () async {
        // Arrange
        const person1 = Person(id: 'person1', name: 'Person 1');
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => [person1.toJson()]);

        // Act & Assert
        expect(
          () => personRepository.deletePerson('non-existent-id'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Person with id "non-existent-id" not found',
          )),
        );
      });

      test('throws StateError when trying to delete the only person in the list', () async {
        // Arrange
        const person1 = Person(id: 'person1', name: 'Person 1');
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.people))
            .thenAnswer((_) async => [person1.toJson()]);

        // Act & Assert
        expect(
          () => personRepository.deletePerson('person1'),
          throwsA(isA<StateError>().having(
            (e) => e.message,
            'message',
            'Cannot delete the only person in the list',
          )),
        );
      });
    });
  });
}
