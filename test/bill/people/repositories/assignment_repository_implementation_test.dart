import 'package:bill_splitter_2/bill/people/models/assignment.dart';
import 'package:bill_splitter_2/bill/people/repositories/assignment_repository_implementation.dart';
import 'package:bill_splitter_2/local_storage/models/local_storage_key.dart';
import 'package:bill_splitter_2/local_storage/services/local_storage_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLocalStorageService extends Mo<PERSON> implements LocalStorageService {
  @override
  Future<void> setValue<T>(LocalStorageKey key, T value) {
    return super.noSuchMethod(
      Invocation.method(#setValue, [key, value]),
    ) ?? Future<void>.value();
  }
}

void main() {
  late MockLocalStorageService mockLocalStorageService;
  late AssignmentRepositoryImplementation assignmentRepository;

  setUpAll(() {
    registerFallbackValue(LocalStorageKey.assignments);
  });

  setUp(() {
    mockLocalStorageService = MockLocalStorageService();

    // Set up default mock behavior for setValue
    when(() => mockLocalStorageService.setValue(any(), any()))
        .thenAnswer((_) => Future<void>.value());

    assignmentRepository = AssignmentRepositoryImplementation(
      localStorageService: mockLocalStorageService,
    );
  });

  group('AssignmentRepositoryImplementation', () {
    group('getAssignmentList', () {
      test('returns empty list when no assignments are stored', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => null);

        // Act
        final result = await assignmentRepository.getAssignmentList();

        // Assert
        expect(result, isEmpty);
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .called(1);
      });

      test('returns list of assignments when assignments are stored', () async {
        // Arrange
        final assignmentsJson = [
          {
            'itemId': 'item1',
            'personId': 'person1',
            'quantity': 2,
            'amount': 15.50,
          },
          {
            'itemId': 'item1',
            'personId': 'person2',
            'quantity': 1,
            'amount': 7.75,
          },
          {
            'itemId': 'item2',
            'personId': 'person1',
            'quantity': 0,
            'amount': 10.00,
          },
        ];
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => assignmentsJson);

        // Act
        final result = await assignmentRepository.getAssignmentList();

        // Assert
        expect(result, hasLength(3));
        expect(result[0].itemId, equals('item1'));
        expect(result[0].personId, equals('person1'));
        expect(result[0].quantity, equals(2));
        expect(result[0].amount, equals(15.50));
        expect(result[1].itemId, equals('item1'));
        expect(result[1].personId, equals('person2'));
        expect(result[1].quantity, equals(1));
        expect(result[1].amount, equals(7.75));
        expect(result[2].itemId, equals('item2'));
        expect(result[2].personId, equals('person1'));
        expect(result[2].quantity, equals(0));
        expect(result[2].amount, equals(10.00));
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .called(1);
      });
    });

    group('createAssignment', () {
      test('creates a new assignment when no assignments exist', () async {
        // Arrange
        const newAssignment = Assignment(
          itemId: 'item1',
          personId: 'person1',
          quantity: 2,
          amount: 15.50,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => null);

        // Act
        final result = await assignmentRepository.createAssignment(newAssignment);

        // Assert
        expect(result, hasLength(1));
        expect(result[0].itemId, equals('item1'));
        expect(result[0].personId, equals('person1'));
        expect(result[0].quantity, equals(2));
        expect(result[0].amount, equals(15.50));
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .called(1);
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.assignments,
          any(),
        )).called(1);
      });

      test('adds assignment to existing assignments list', () async {
        // Arrange
        const existingAssignment = Assignment(
          itemId: 'item1',
          personId: 'person1',
          quantity: 1,
          amount: 10.00,
        );
        const newAssignment = Assignment(
          itemId: 'item1',
          personId: 'person2',
          quantity: 2,
          amount: 20.00,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [existingAssignment.toJson()]);

        // Act
        final result = await assignmentRepository.createAssignment(newAssignment);

        // Assert
        expect(result, hasLength(2));
        expect(result[0].itemId, equals('item1'));
        expect(result[0].personId, equals('person1'));
        expect(result[1].itemId, equals('item1'));
        expect(result[1].personId, equals('person2'));
        expect(result[1].quantity, equals(2));
        expect(result[1].amount, equals(20.00));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.assignments,
          any(),
        )).called(1);
      });

      test('replaces existing assignment with same itemId and personId', () async {
        // Arrange
        const existingAssignment = Assignment(
          itemId: 'item1',
          personId: 'person1',
          quantity: 1,
          amount: 10.00,
        );
        const updatedAssignment = Assignment(
          itemId: 'item1',
          personId: 'person1',
          quantity: 3,
          amount: 30.00,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [existingAssignment.toJson()]);

        // Act
        final result = await assignmentRepository.createAssignment(updatedAssignment);

        // Assert
        expect(result, hasLength(1));
        expect(result[0].itemId, equals('item1'));
        expect(result[0].personId, equals('person1'));
        expect(result[0].quantity, equals(3));
        expect(result[0].amount, equals(30.00));
      });
    });

    group('updateAssignment', () {
      test('updates existing assignment in the list', () async {
        // Arrange
        const originalAssignment = Assignment(
          itemId: 'item1',
          personId: 'person1',
          quantity: 1,
          amount: 10.00,
        );
        const updatedAssignment = Assignment(
          itemId: 'item1',
          personId: 'person1',
          quantity: 2,
          amount: 20.00,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [originalAssignment.toJson()]);

        // Act
        final result = await assignmentRepository.updateAssignment(updatedAssignment);

        // Assert
        expect(result, hasLength(1));
        expect(result[0].itemId, equals('item1'));
        expect(result[0].personId, equals('person1'));
        expect(result[0].quantity, equals(2));
        expect(result[0].amount, equals(20.00));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.assignments,
          any(),
        )).called(1);
      });

      test('updates correct assignment when multiple assignments exist', () async {
        // Arrange
        const assignment1 = Assignment(itemId: 'item1', personId: 'person1', quantity: 1, amount: 10.00);
        const assignment2 = Assignment(itemId: 'item1', personId: 'person2', quantity: 2, amount: 20.00);
        const assignment3 = Assignment(itemId: 'item2', personId: 'person1', quantity: 1, amount: 15.00);
        const updatedAssignment2 = Assignment(
          itemId: 'item1',
          personId: 'person2',
          quantity: 3,
          amount: 30.00,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [
              assignment1.toJson(),
              assignment2.toJson(),
              assignment3.toJson(),
            ]);

        // Act
        final result = await assignmentRepository.updateAssignment(updatedAssignment2);

        // Assert
        expect(result, hasLength(3));
        expect(result[0].quantity, equals(1)); // assignment1 unchanged
        expect(result[1].quantity, equals(3)); // assignment2 updated
        expect(result[1].amount, equals(30.00));
        expect(result[2].quantity, equals(1)); // assignment3 unchanged
      });

      test('throws ArgumentError when assignment to update does not exist', () async {
        // Arrange
        const assignment1 = Assignment(itemId: 'item1', personId: 'person1', quantity: 1, amount: 10.00);
        const nonExistentAssignment = Assignment(
          itemId: 'item2',
          personId: 'person2',
          quantity: 2,
          amount: 20.00,
        );
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [assignment1.toJson()]);

        // Act & Assert
        expect(
          () => assignmentRepository.updateAssignment(nonExistentAssignment),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Assignment with itemId "item2" and personId "person2" not found',
          )),
        );
      });
    });

    group('deleteAssignment', () {
      test('deletes assignment from the list', () async {
        // Arrange
        const assignment1 = Assignment(itemId: 'item1', personId: 'person1', quantity: 1, amount: 10.00);
        const assignment2 = Assignment(itemId: 'item1', personId: 'person2', quantity: 2, amount: 20.00);
        const assignment3 = Assignment(itemId: 'item2', personId: 'person1', quantity: 1, amount: 15.00);
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [
              assignment1.toJson(),
              assignment2.toJson(),
              assignment3.toJson(),
            ]);

        // Act
        final result = await assignmentRepository.deleteAssignment('item1', 'person2');

        // Assert
        expect(result, hasLength(2));
        expect(result[0].itemId, equals('item1'));
        expect(result[0].personId, equals('person1'));
        expect(result[1].itemId, equals('item2'));
        expect(result[1].personId, equals('person1'));
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.assignments,
          any(),
        )).called(1);
      });

      test('throws ArgumentError when assignment to delete does not exist', () async {
        // Arrange
        const assignment1 = Assignment(itemId: 'item1', personId: 'person1', quantity: 1, amount: 10.00);
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [assignment1.toJson()]);

        // Act & Assert
        expect(
          () => assignmentRepository.deleteAssignment('item2', 'person2'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Assignment with itemId "item2" and personId "person2" not found',
          )),
        );
      });

      test('returns empty list when deleting the only assignment', () async {
        // Arrange
        const assignment1 = Assignment(itemId: 'item1', personId: 'person1', quantity: 1, amount: 10.00);
        when(() => mockLocalStorageService.getValue<List<dynamic>>(LocalStorageKey.assignments))
            .thenAnswer((_) async => [assignment1.toJson()]);

        // Act
        final result = await assignmentRepository.deleteAssignment('item1', 'person1');

        // Assert
        expect(result, isEmpty);
        verify(() => mockLocalStorageService.setValue(
          LocalStorageKey.assignments,
          any(),
        )).called(1);
      });
    });
  });
}
