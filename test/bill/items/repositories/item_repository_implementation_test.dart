import 'package:bill_splitter_2/bill/items/models/item.dart';
import 'package:bill_splitter_2/bill/items/repositories/item_repository_implementation.dart';
import 'package:bill_splitter_2/local_storage/models/local_storage_key.dart';
import 'package:bill_splitter_2/local_storage/services/local_storage_service.dart';
import 'package:bill_splitter_2/unique_identifier/services/unique_identifier_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLocalStorageService extends Mock implements LocalStorageService {
  @override
  Future<void> setValue<T>(LocalStorageKey key, T value) {
    return super.noSuchMethod(
      Invocation.method(#setValue, [key, value]),
    ) ?? Future<void>.value();
  }
}

class MockUniqueIdentifierService extends Mock implements UniqueIdentifierService {}

void main() {
  late MockLocalStorageService mockLocalStorageService;
  late MockUniqueIdentifierService mockUniqueIdentifierService;
  late ItemRepositoryImplementation itemRepository;

  setUpAll(() {
    registerFallbackValue(LocalStorageKey.items);
  });

  setUp(() {
    mockLocalStorageService = MockLocalStorageService();
    mockUniqueIdentifierService = MockUniqueIdentifierService();

    // Set up default mock behavior for setValue
    when(() => mockLocalStorageService.setValue(any(), any()))
        .thenAnswer((_) => Future<void>.value());

    itemRepository = ItemRepositoryImplementation(
      localStorageService: mockLocalStorageService,
      uniqueIdentifierService: mockUniqueIdentifierService,
    );
  });

  group('ItemRepositoryImplementation', () {
    group('getItemList', () {
      test('returns empty list when no items exist in storage', () async {
        // Arrange
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => null);

        // Act
        final result = await itemRepository.getItemList();

        // Assert
        expect(result, isEmpty);
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).called(1);
      });

      test('returns list of items when items exist in storage', () async {
        // Arrange
        final itemListJson = [
          {'id': '1', 'name': 'Item 1', 'price': 10.0, 'quantity': 2, 'total': 20.0},
          {'id': '2', 'name': 'Item 2', 'price': 15.0, 'quantity': 1, 'total': 15.0},
        ];
        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => itemListJson);

        // Act
        final result = await itemRepository.getItemList();

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('1'));
        expect(result[0].name, equals('Item 1'));
        expect(result[0].price, equals(10.0));
        expect(result[0].quantity, equals(2));
        expect(result[0].total, equals(20.0));
        expect(result[1].id, equals('2'));
        expect(result[1].name, equals('Item 2'));
        verify(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).called(1);
      });
    });

    group('createItem', () {
      test('creates new item and adds it to existing list', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Existing Item', price: 5.0, quantity: 1, total: 5.0),
        ];
        final existingItemsJson = existingItems.map((item) => item.toJson()).toList();
        final newItemId = 'new-item-id';

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => existingItemsJson);
        when(() => mockUniqueIdentifierService.generate())
            .thenReturn(newItemId);
        when(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).thenAnswer((_) => Future<void>.value());

        // Act
        final result = await itemRepository.createItem();

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('1')); // Existing item
        expect(result[1].id, equals(newItemId)); // New item
        expect(result[1].name, equals('')); // Default name
        expect(result[1].price, equals(0)); // Default price
        expect(result[1].quantity, equals(1)); // Default quantity
        expect(result[1].total, equals(0)); // Default total

        verify(() => mockUniqueIdentifierService.generate()).called(1);
        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).called(1);
      });

      test('creates first item when list is empty', () async {
        // Arrange
        final newItemId = 'first-item-id';

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => null);
        when(() => mockUniqueIdentifierService.generate())
            .thenReturn(newItemId);
        when(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).thenAnswer((_) => Future<void>.value());

        // Act
        final result = await itemRepository.createItem();

        // Assert
        expect(result, hasLength(1));
        expect(result[0].id, equals(newItemId));
        expect(result[0].name, equals(''));
        expect(result[0].price, equals(0));
        expect(result[0].quantity, equals(1));
        expect(result[0].total, equals(0));

        verify(() => mockUniqueIdentifierService.generate()).called(1);
        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).called(1);
      });
    });

    group('updateItem', () {
      test('updates existing item in the list', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
          Item(id: '2', name: 'Item 2', price: 15.0, quantity: 2, total: 30.0),
        ];
        final existingItemsJson = existingItems.map((item) => item.toJson()).toList();
        final updatedItem = Item(id: '1', name: 'Updated Item', price: 12.0, quantity: 3, total: 36.0);

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => existingItemsJson);
        when(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).thenAnswer((_) => Future<void>.value());

        // Act
        final result = await itemRepository.updateItem(updatedItem);

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('1'));
        expect(result[0].name, equals('Updated Item'));
        expect(result[0].price, equals(12.0));
        expect(result[0].quantity, equals(3));
        expect(result[0].total, equals(36.0));
        expect(result[1].id, equals('2')); // Unchanged item

        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).called(1);
      });
    });

    group('deleteItem', () {
      test('removes item from the list by id', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
          Item(id: '2', name: 'Item 2', price: 15.0, quantity: 2, total: 30.0),
          Item(id: '3', name: 'Item 3', price: 20.0, quantity: 1, total: 20.0),
        ];
        final existingItemsJson = existingItems.map((item) => item.toJson()).toList();

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => existingItemsJson);
        when(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).thenAnswer((_) => Future<void>.value());

        // Act
        final result = await itemRepository.deleteItem('2');

        // Assert
        expect(result, hasLength(2));
        expect(result[0].id, equals('1'));
        expect(result[1].id, equals('3'));
        expect(result.any((item) => item.id == '2'), isFalse);

        verify(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            )).called(1);
      });

      test('throws exception when trying to delete the only item', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Only Item', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingItemsJson = existingItems.map((item) => item.toJson()).toList();

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => existingItemsJson);

        // Act & Assert
        expect(
          () => itemRepository.deleteItem('1'),
          throwsA(isA<StateError>().having(
            (e) => e.message,
            'message',
            'Cannot delete the only item in the list',
          )),
        );

        verifyNever(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            ));
      });

      test('throws exception when trying to delete an item that does not exist', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
          Item(id: '2', name: 'Item 2', price: 15.0, quantity: 2, total: 30.0),
        ];
        final existingItemsJson = existingItems.map((item) => item.toJson()).toList();

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => existingItemsJson);

        // Act & Assert
        expect(
          () => itemRepository.deleteItem('non-existent-id'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Item with id "non-existent-id" not found',
          )),
        );

        verifyNever(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            ));
      });

      test('throws ArgumentError when trying to delete non-existent item from single-item list', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Only Item', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingItemsJson = existingItems.map((item) => item.toJson()).toList();

        when(() => mockLocalStorageService.getValue<List<dynamic>>(
              LocalStorageKey.items,
            )).thenAnswer((_) async => existingItemsJson);

        // Act & Assert
        expect(
          () => itemRepository.deleteItem('non-existent-id'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Item with id "non-existent-id" not found',
          )),
        );

        verifyNever(() => mockLocalStorageService.setValue(
              LocalStorageKey.items,
              any(),
            ));
      });
    });
  });
}
